<template>
  <AdaptiveLayout>
    <!-- PC端布局 -->
    <template #pc>
      <div class="pc-success-container">
        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <Icon name="material-symbols:sync" size="64" class="loading-icon" />
          <p>Processing your payment...</p>
        </div>

        <!-- Success State -->
        <div v-else-if="orderInfo" class="success-content">
          <!-- Success Icon -->
          <div class="success-icon">
            <Icon
              name="material-symbols:check-circle"
              size="80"
              class="check-icon"
            />
          </div>

          <!-- Title -->
          <h1 class="success-title">Payment Successful!</h1>

          <!-- Message -->
          <p class="success-message">
            Congratulations! Your payment has been processed successfully.
            Diamonds have been added to your account and you can now enjoy the
            full AI interactive experience.
          </p>

          <!-- Amount Info Card -->
          <div class="amount-card">
            <h3 class="card-title">Payment Details</h3>
            <div class="amount-details">
              <div v-if="orderInfo.amount > 0" class="detail-row">
                <span class="label">Amount Paid:</span>
                <span class="value">${{ orderInfo.amount }}</span>
              </div>
              <div v-if="orderInfo.coins > 0" class="detail-row">
                <span class="label">Diamonds Received:</span>
                <span class="value diamonds">{{ orderInfo.coins }} 💎</span>
              </div>
              <div v-if="orderInfo.orderId" class="detail-row">
                <span class="label">Order ID:</span>
                <span class="value order-id">{{ orderInfo.orderId }}</span>
              </div>
              <div v-if="orderInfo.payTime" class="detail-row">
                <span class="label">Payment Time:</span>
                <span class="value">{{
                  formatPayTime(orderInfo.payTime)
                }}</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <button class="btn btn-primary" @click="goToHome">
              Start Experience
            </button>
            <button class="btn btn-secondary" @click="goToProfile">
              View My Account
            </button>
          </div>
        </div>

        <!-- Error State -->
        <div v-else class="error-state">
          <Icon name="material-symbols:error" size="80" class="error-icon" />
          <h1 class="error-title">Payment Processing</h1>
          <p class="error-message">
            We're verifying your payment. Please wait a moment or contact
            support if this persists.
          </p>
          <button class="btn btn-primary" @click="retryLoad"> Retry </button>
        </div>
      </div>
    </template>

    <!-- 移动端布局 -->
    <template #mobile>
      <div class="mobile-success-container">
        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <Icon name="material-symbols:sync" size="48" class="loading-icon" />
          <p>Processing your payment...</p>
        </div>

        <!-- Success State -->
        <div v-else-if="orderInfo" class="success-content">
          <!-- Success Icon -->
          <div class="success-icon">
            <Icon
              name="material-symbols:check-circle"
              size="64"
              class="check-icon"
            />
          </div>

          <!-- Title -->
          <h1 class="success-title">Payment Successful!</h1>

          <!-- Message -->
          <p class="success-message">
            Congratulations! Your payment has been processed and diamonds have
            been added to your account.
          </p>

          <!-- Amount Info -->
          <div
            v-if="orderInfo.amount > 0 || orderInfo.coins > 0"
            class="amount-info"
          >
            <div v-if="orderInfo.amount > 0" class="amount-item">
              <span class="label">Amount Paid:</span>
              <span class="value">${{ orderInfo.amount }}</span>
            </div>
            <div v-if="orderInfo.coins > 0" class="amount-item">
              <span class="label">Diamonds Received:</span>
              <span class="value diamonds">{{ orderInfo.coins }} 💎</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <button class="btn btn-primary" @click="goToHome">
              Start Experience
            </button>
            <button class="btn btn-secondary" @click="goToProfile">
              Check Balance
            </button>
          </div>
        </div>

        <!-- Error State -->
        <div v-else class="error-state">
          <Icon name="material-symbols:error" size="64" class="error-icon" />
          <h1 class="error-title">Payment Processing</h1>
          <p class="error-message">
            We're verifying your payment. Please wait a moment or contact
            support if this persists.
          </p>
          <button class="btn btn-primary" @click="retryLoad"> Retry </button>
        </div>
      </div>
    </template>
  </AdaptiveLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Composables
const { brandingConfig } = useBranding()

// SEO Meta
useHead({
  title: `Payment Successful - ${brandingConfig.value.websiteTitle}`,
  meta: [
    {
      name: 'description',
      content: 'Payment successful, diamonds credited to your account',
    },
    { name: 'robots', content: 'noindex, nofollow' },
  ],
})

// 页面元数据
definePageMeta({
  layout: false,
  title: () => {
    const { brandingConfig } = useBranding()
    return `Payment Successful - ${brandingConfig.value.websiteTitle}`
  },
})

// Composables
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// State
const loading = ref(true)
const orderInfo = ref<{
  amount: number
  coins: number
  orderId?: string
  payTime?: number
} | null>(null)

// Lifecycle
onMounted(async () => {
  await loadOrderInfo()
  // 更新用户信息
  await refreshUserInfo()
  loading.value = false
})

// Methods
const loadOrderInfo = async () => {
  // 从 URL 参数获取支付信息
  const amount = route.query.amount as string
  const payOrderId = route.query.payOrderId as string
  const mchOrderNo = route.query.mchOrderNo as string
  const paySuccTime = route.query.paySuccTime as string
  const resultCode = route.query.resultCode as string
  const sessionId = route.query.session_id as string // Stripe 参数
  const provider = route.query.provider as string // 支付提供商

  // 检查支付是否成功
  // OnlyPay: 需要 resultCode === 'SUCCESS'
  // Stripe: 有 provider === 'stripe' 和 session_id 就认为成功
  const isOnlyPaySuccess = resultCode === 'SUCCESS'
  const isStripeSuccess = provider === 'stripe' && !!sessionId

  if (!isOnlyPaySuccess && !isStripeSuccess) {
    console.error('Payment failed or pending:', {
      resultCode,
      sessionId,
      provider,
    })
    return
  }

  if (amount) {
    const amountValue = parseFloat(amount)
    orderInfo.value = {
      amount: amountValue,
      coins: Math.round(amountValue * 100), // amount * 100 = 钻石数量
      orderId: payOrderId || mchOrderNo || sessionId,
      payTime: paySuccTime ? parseInt(paySuccTime) : Date.now(),
    }

    // 清理 URL 参数（避免敏感信息留在浏览器历史中）
    setTimeout(() => {
      router.replace({ path: '/recharge-success' })
    }, 1000)
  } else if (isStripeSuccess) {
    // 如果没有 amount 参数，但是 Stripe 支付成功
    // 这种情况下我们显示一个通用的成功信息，并尝试从用户信息中获取最新余额
    orderInfo.value = {
      amount: 0, // 未知金额，将在模板中隐藏金额显示
      coins: 0, // 未知钻石数量，将在模板中隐藏钻石显示
      orderId: sessionId,
      payTime: Date.now(),
    }

    // 清理 URL 参数
    setTimeout(() => {
      router.replace({ path: '/recharge-success' })
    }, 1000)
  }
}

const refreshUserInfo = async () => {
  try {
    // 调用 getUserInfo 更新用户信息
    await userStore.getUserInfo()
  } catch (error) {
    console.error('Failed to refresh user info:', error)
    // 不影响页面显示，用户信息更新失败不会阻止用户看到成功页面
  }
}

const formatPayTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const goToHome = () => {
  router.push('/')
}

const goToProfile = () => {
  router.push('/user/profile')
}

const retryLoad = () => {
  loading.value = true
  loadOrderInfo().then(() => {
    loading.value = false
  })
}
</script>

<style lang="less" scoped>
// Mobile Styles
.mobile-success-container {
  padding: 60px 20px 40px;
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);

  .loading-state,
  .success-content,
  .error-state {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 20px 40px var(--shadow-color);
    max-width: 400px;
    width: 100%;
    border: 1px solid var(--border-color);
  }

  .loading-state {
    .loading-icon {
      color: var(--accent-color);
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: var(--text-secondary);
    }
  }

  .error-state {
    .error-icon {
      color: var(--error-color);
      margin-bottom: 20px;
    }

    .error-title {
      font-size: 24px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 12px;
    }

    .error-message {
      font-size: 16px;
      color: var(--text-secondary);
      line-height: 1.5;
      margin-bottom: 24px;
    }
  }

  .success-content {
    .success-icon {
      margin-bottom: 20px;

      .check-icon {
        color: var(--success-color);
        animation: checkBounce 0.6s ease-out;
      }
    }

    .success-title {
      font-size: 28px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 12px;
    }

    .success-message {
      font-size: 16px;
      color: var(--text-secondary);
      line-height: 1.5;
      margin-bottom: 30px;
    }

    .amount-info {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 30px;

      .amount-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: var(--text-secondary);
        }

        .value {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);

          &.diamonds {
            color: var(--accent-color);
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .btn {
      padding: 14px 24px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;

      &.btn-primary {
        background: var(--accent-color);
        color: var(--text-on-accent);

        &:hover {
          background: var(--accent-hover);
          transform: translateY(-1px);
        }
      }

      &.btn-secondary {
        background: var(--bg-hover);
        color: var(--text-secondary);
        border: 1px solid var(--border-color);

        &:hover {
          background: var(--bg-secondary);
          color: var(--text-primary);
        }
      }
    }
  }
}

// PC Styles
.pc-success-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 80px 40px;
  text-align: center;
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  flex-direction: column;
  justify-content: center;

  .loading-state,
  .success-content,
  .error-state {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px var(--shadow-color);
    border: 1px solid var(--border-color);
  }

  .loading-state {
    .loading-icon {
      color: var(--accent-color);
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    p {
      font-size: 18px;
      color: var(--text-secondary);
    }
  }

  .error-state {
    .error-icon {
      color: var(--error-color);
      margin-bottom: 30px;
    }

    .error-title {
      font-size: 32px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 16px;
    }

    .error-message {
      font-size: 18px;
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: 30px;
    }
  }

  .success-content {
    .success-icon {
      margin-bottom: 30px;

      .check-icon {
        color: var(--success-color);
        animation: checkBounce 0.6s ease-out;
      }
    }

    .success-title {
      font-size: 36px;
      font-weight: bold;
      color: var(--text-primary);
      margin-bottom: 16px;
    }

    .success-message {
      font-size: 18px;
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: 40px;
    }

    .amount-card {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 16px;
      padding: 30px;
      margin-bottom: 40px;
      box-shadow: 0 10px 30px var(--shadow-color);

      .card-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 20px;
      }

      .amount-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color);

          &:last-child {
            border-bottom: none;
          }

          .label {
            font-size: 16px;
            color: var(--text-secondary);
          }

          .value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);

            &.diamonds {
              color: var(--accent-color);
              font-size: 18px;
            }

            &.order-id {
              font-family: monospace;
              font-size: 14px;
              color: var(--text-tertiary);
              word-break: break-all;
              overflow-wrap: break-word;
            }
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;

    .btn {
      padding: 16px 32px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 160px;

      &.btn-primary {
        background: var(--accent-color);
        color: var(--text-on-accent);

        &:hover {
          background: var(--accent-hover);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px var(--accent-bg);
        }
      }

      &.btn-secondary {
        background: var(--bg-hover);
        color: var(--text-secondary);
        border: 1px solid var(--border-color);

        &:hover {
          background: var(--bg-secondary);
          color: var(--text-primary);
          border-color: var(--accent-color);
        }
      }
    }
  }
}

// Animations
@keyframes checkBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .pc-success-container {
    padding: 40px 20px;

    .success-title {
      font-size: 28px;
    }

    .success-message {
      font-size: 16px;
    }

    .action-buttons {
      flex-direction: column;

      .btn {
        min-width: auto;
      }
    }
  }
}
</style>
